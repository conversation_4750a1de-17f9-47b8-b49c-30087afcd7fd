version: "3.8"
services:
  mariadb:
    image: mariadb:10.8
    command:
      - --character-set-server=utf8mb4
      - --collation-server=utf8mb4_unicode_ci
      - --skip-character-set-client-handshake
      - --skip-innodb-read-only-compressed # Temporary fix for MariaDB 10.6
    environment:
      MYSQL_ROOT_PASSWORD: 123
    volumes:
      - mariadb-data:/var/lib/mysql

  redis:
    image: redis:alpine

  # Frontend development server with hot reload and file watching
  frontend:
    image: node:18-alpine
    working_dir: /app
    volumes:
      - ../frontend:/app
      - frontend_node_modules:/app/node_modules
    ports:
      - 8080:8080
    command: sh -c "yarn install && yarn dev --host 0.0.0.0"
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true # Enable file watching in Docker
      - WATCHPACK_POLLING=true
    depends_on:
      - frappe
    restart: unless-stopped

  # Roster frontend with hot reload (if you use it)
  roster:
    image: node:18-alpine
    working_dir: /app
    volumes:
      - ../roster:/app
      - roster_node_modules:/app/node_modules
    ports:
      - 8081:8081
    command: sh -c "yarn install && yarn dev --host 0.0.0.0"
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
    depends_on:
      - frappe
    restart: unless-stopped

  # Auto-build service that watches for changes and rebuilds
  frontend-builder:
    image: node:18-alpine
    working_dir: /workspace/hrms
    volumes:
      - ../:/workspace/hrms
      - frontend_node_modules:/workspace/hrms/frontend/node_modules
    command: sh -c "apk add --no-cache inotify-tools && chmod +x /workspace/hrms/docker/watch-and-build.sh && /workspace/hrms/docker/watch-and-build.sh"
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
    depends_on:
      - frappe
    restart: unless-stopped

  frappe:
    image: frappe/bench:latest
    command: bash /workspace/init.sh
    environment:
      - SHELL=/bin/bash
    working_dir: /home/<USER>
    volumes:
      - .:/workspace
      - ../:/workspace/hrms # Mount the entire HRMS directory
    ports:
      - 8000:8000
      - 9000:9000
    depends_on:
      - mariadb
      - redis

volumes:
  mariadb-data:
  frontend_node_modules:
  roster_node_modules:
