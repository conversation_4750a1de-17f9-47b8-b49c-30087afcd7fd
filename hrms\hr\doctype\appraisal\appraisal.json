{"actions": [], "autoname": "naming_series:", "creation": "2022-08-26 05:55:37.571091", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["employee_details_tab", "naming_series", "employee", "employee_name", "department", "designation", "employee_image", "column_break0", "company", "appraisal_cycle", "start_date", "end_date", "section_break_aeb0", "final_score", "kra_tab", "appraisal_template", "rate_goals_manually", "section_break_kras", "appraisal_kra", "goal_score_percentage", "section_break_goals", "goals", "remarks", "total_section", "total_score", "feedback_tab", "feedback_html", "section_break_20", "avg_feedback_score", "self_appraisal_tab", "section_break_23", "self_ratings", "self_score", "reflections_section", "reflections", "amended_from"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "options": "HR-APR-.YYYY.-", "print_hide": 1, "reqd": 1, "set_only_once": 1}, {"fieldname": "employee", "fieldtype": "Link", "in_global_search": 1, "in_standard_filter": 1, "label": "Employee", "oldfieldname": "employee", "oldfieldtype": "Link", "options": "Employee", "reqd": 1, "search_index": 1}, {"fetch_from": "employee.employee_name", "fieldname": "employee_name", "fieldtype": "Data", "in_global_search": 1, "label": "Employee Name", "oldfieldname": "employee_name", "oldfieldtype": "Data", "read_only": 1}, {"fieldname": "column_break0", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "width": "50%"}, {"fetch_from": "employee.department", "fieldname": "department", "fieldtype": "Link", "label": "Department", "options": "Department", "read_only": 1}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "remember_last_selected_value": 1, "reqd": 1}, {"fetch_from": "employee.designation", "fieldname": "designation", "fieldtype": "Link", "label": "Designation", "options": "Designation", "read_only": 1}, {"fieldname": "appraisal_cycle", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Appraisal Cycle", "options": "Appraisal Cycle", "reqd": 1}, {"depends_on": "eval: !doc.rate_goals_manually", "fieldname": "appraisal_kra", "fieldtype": "Table", "label": "KRA vs Goals", "oldfieldname": "appraisal_details", "oldfieldtype": "Table", "options": "Appraisal KRA"}, {"fieldname": "total_section", "fieldtype": "Section Break"}, {"description": "Out of 5", "fieldname": "total_score", "fieldtype": "Float", "in_list_view": 1, "label": "Total Goal Score", "no_copy": 1, "oldfieldname": "total_score", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "feedback_html", "fieldtype": "HTML", "label": "Feedback HTML"}, {"fieldname": "self_appraisal_tab", "fieldtype": "Tab Break", "label": "Self Appraisal"}, {"fieldname": "reflections_section", "fieldtype": "Section Break", "label": "Reflections"}, {"fieldname": "self_score", "fieldtype": "Float", "label": "Total Self Score", "read_only": 1}, {"fieldname": "avg_feedback_score", "fieldtype": "Float", "hidden": 1, "label": "Average Feedback Score", "read_only": 1}, {"fieldname": "section_break_20", "fieldtype": "Section Break"}, {"allow_on_submit": 1, "fetch_from": "employee.image", "fieldname": "employee_image", "fieldtype": "Attach Image", "hidden": 1, "label": "Employee Image"}, {"fieldname": "feedback_tab", "fieldtype": "Tab Break", "label": "<PERSON><PERSON><PERSON>"}, {"fieldname": "appraisal_template", "fieldtype": "Link", "in_standard_filter": 1, "label": "Appraisal Template", "mandatory_depends_on": "eval:!doc.__islocal", "oldfieldname": "kra_template", "oldfieldtype": "Link", "options": "Appraisal Template"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Appraisal", "print_hide": 1, "read_only": 1}, {"fieldname": "employee_details_tab", "fieldtype": "Tab Break", "label": "Overview", "oldfieldtype": "Section Break"}, {"fieldname": "section_break_23", "fieldtype": "Section Break", "label": "Ratings"}, {"fieldname": "reflections", "fieldtype": "Text Editor"}, {"depends_on": "rate_goals_manually", "fieldname": "goals", "fieldtype": "Table", "label": "Goals", "options": "Appraisal Goal"}, {"depends_on": "rate_goals_manually", "description": "Any other remarks, noteworthy effort that should go in the records", "fieldname": "remarks", "fieldtype": "Text", "label": "Remarks"}, {"fieldname": "section_break_kras", "fieldtype": "Section Break"}, {"fieldname": "section_break_goals", "fieldtype": "Section Break"}, {"default": "0", "fieldname": "rate_goals_manually", "fieldtype": "Check", "label": "Rate Goals Manually", "read_only": 1}, {"fieldname": "start_date", "fieldtype": "Date", "label": "Start Date", "read_only": 1}, {"fieldname": "end_date", "fieldtype": "Date", "label": "End Date", "read_only": 1}, {"fieldname": "kra_tab", "fieldtype": "Tab Break", "label": "KRAs", "oldfieldtype": "Section Break", "show_dashboard": 1}, {"depends_on": "eval: !doc.rate_goals_manually", "fieldname": "goal_score_percentage", "fieldtype": "Float", "label": "Goal Score (%)", "read_only": 1}, {"fieldname": "self_ratings", "fieldtype": "Table", "options": "Employee Feedback Rating"}, {"fieldname": "section_break_aeb0", "fieldtype": "Section Break"}, {"depends_on": "appraisal_cycle", "description": "Average of Goal Score, Feedback Score, and Self Appraisal Score (out of 5)", "fieldname": "final_score", "fieldtype": "Float", "in_list_view": 1, "label": "Final Score", "read_only": 1}], "icon": "fa fa-thumbs-up", "image_field": "employee_image", "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-02-18 15:03:49.932773", "modified_by": "Administrator", "module": "HR", "name": "Appraisal", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Employee", "share": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "HR User", "share": 1, "submit": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "share": 1, "submit": 1, "write": 1}], "search_fields": "employee, employee_name, appraisal_cycle", "sort_field": "creation", "sort_order": "DESC", "states": [], "timeline_field": "employee", "title_field": "employee_name", "track_changes": 1}