{"based_on": "posting_date", "chart_name": "Expense <PERSON>", "chart_type": "Sum", "color": "#449CF0", "creation": "2022-08-21 14:07:24.120739", "docstatus": 0, "doctype": "Dashboard Chart", "document_type": "Expense <PERSON>", "dynamic_filters_json": "[[\"Expense Claim\",\"company\",\"=\",\"frappe.defaults.get_user_default(\\\"Company\\\")\"]]", "filters_json": "[[\"Expense <PERSON>laim\",\"docstatus\",\"=\",\"1\",false]]", "group_by_type": "Count", "idx": 0, "is_public": 1, "is_standard": 1, "last_synced_on": "2022-09-16 11:36:29.292891", "modified": "2022-09-16 11:37:59.669291", "modified_by": "Administrator", "module": "HR", "name": "Expense <PERSON>", "number_of_groups": 0, "owner": "Administrator", "parent_document_type": "", "roles": [], "source": "", "time_interval": "Monthly", "timeseries": 1, "timespan": "Last Year", "type": "Line", "use_report_chart": 0, "value_based_on": "total_sanctioned_amount", "y_axis": []}