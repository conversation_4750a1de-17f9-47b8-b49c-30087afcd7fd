#!/bin/bash

# Auto-build script for HRMS frontend
# This script watches for changes in the frontend directory and automatically rebuilds

echo "Starting HRMS Frontend Auto-Builder..."
echo "Watching for changes in frontend directory..."

# Function to build frontend
build_frontend() {
    echo "🔄 Building frontend..."
    cd /workspace/hrms/frontend
    
    # Install dependencies if package.json changed
    if [ package.json -nt node_modules/.last_install ] || [ ! -d node_modules ]; then
        echo "📦 Installing dependencies..."
        yarn install --check-files
        touch node_modules/.last_install
    fi
    
    # Build the frontend
    echo "🏗️  Building frontend assets..."
    yarn build
    
    if [ $? -eq 0 ]; then
        echo "✅ Frontend build completed successfully!"
        echo "📁 Assets copied to hrms/public/frontend/"
        
        # Notify Frappe to reload (if bench is running)
        if pgrep -f "bench start" > /dev/null; then
            echo "🔄 Clearing Frappe cache..."
            cd /home/<USER>/frappe-bench
            bench --site hrms.localhost clear-cache
        fi
    else
        echo "❌ Frontend build failed!"
    fi
    
    echo "---"
}

# Function to build roster
build_roster() {
    echo "🔄 Building roster..."
    cd /workspace/hrms/roster
    
    # Install dependencies if package.json changed
    if [ package.json -nt node_modules/.last_install ] || [ ! -d node_modules ]; then
        echo "📦 Installing roster dependencies..."
        yarn install --check-files
        touch node_modules/.last_install
    fi
    
    # Build the roster
    echo "🏗️  Building roster assets..."
    yarn build
    
    if [ $? -eq 0 ]; then
        echo "✅ Roster build completed successfully!"
        echo "📁 Assets copied to hrms/public/roster/"
    else
        echo "❌ Roster build failed!"
    fi
    
    echo "---"
}

# Initial build
echo "🚀 Performing initial build..."
build_frontend
build_roster

# Install inotify-tools if not present
if ! command -v inotifywait &> /dev/null; then
    echo "Installing inotify-tools..."
    apk add --no-cache inotify-tools
fi

# Watch for changes
echo "👀 Watching for file changes..."
echo "Press Ctrl+C to stop watching"

# Watch frontend directory
inotifywait -m -r -e modify,create,delete,move \
    --exclude '(node_modules|\.git|dist|build|\.cache)' \
    /workspace/hrms/frontend /workspace/hrms/roster 2>/dev/null |
while read path action file; do
    echo "📝 Detected change: $action $path$file"
    
    # Debounce: wait a bit for multiple rapid changes
    sleep 1
    
    # Determine which project to build based on path
    if [[ "$path" == *"/frontend/"* ]]; then
        build_frontend
    elif [[ "$path" == *"/roster/"* ]]; then
        build_roster
    fi
done
