{"based_on": "", "chart_name": "Department wise Expense <PERSON>laims", "chart_type": "Group By", "creation": "2022-08-31 23:06:51.144716", "docstatus": 0, "doctype": "Dashboard Chart", "document_type": "Expense <PERSON>", "dynamic_filters_json": "[[\"Expense Claim\",\"company\",\"=\",\"frappe.defaults.get_user_default(\\\"Company\\\")\"]]", "filters_json": "[[\"Expense <PERSON>laim\",\"docstatus\",\"=\",\"1\",false]]", "group_by_based_on": "department", "group_by_type": "Count", "idx": 0, "is_public": 1, "is_standard": 1, "last_synced_on": "2022-09-16 12:36:29.444007", "modified": "2022-09-16 11:41:32.160907", "modified_by": "Administrator", "module": "HR", "name": "Department wise Expense <PERSON>laims", "number_of_groups": 0, "owner": "Administrator", "parent_document_type": "", "roles": [], "source": "", "time_interval": "Yearly", "timeseries": 0, "timespan": "Last Year", "type": "Bar", "use_report_chart": 0, "value_based_on": "", "y_axis": []}