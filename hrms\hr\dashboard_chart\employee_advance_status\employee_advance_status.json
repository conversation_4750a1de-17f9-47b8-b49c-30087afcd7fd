{"based_on": "", "chart_name": "Employee Advance Status", "chart_type": "Group By", "creation": "2022-08-31 23:06:16.063039", "docstatus": 0, "doctype": "Dashboard Chart", "document_type": "Employee Advance", "dynamic_filters_json": "[[\"Employee Advance\",\"company\",\"=\",\"frappe.defaults.get_user_default(\\\"Company\\\")\"]]", "filters_json": "[[\"Employee Advance\",\"docstatus\",\"=\",\"1\",false]]", "group_by_based_on": "status", "group_by_type": "Count", "idx": 0, "is_public": 1, "is_standard": 1, "last_synced_on": "2022-09-16 12:36:29.430589", "modified": "2022-09-16 11:43:57.244256", "modified_by": "Administrator", "module": "HR", "name": "Employee Advance Status", "number_of_groups": 0, "owner": "Administrator", "parent_document_type": "", "roles": [], "source": "", "time_interval": "Yearly", "timeseries": 0, "timespan": "Last Year", "type": "Pie", "use_report_chart": 0, "value_based_on": "", "y_axis": []}