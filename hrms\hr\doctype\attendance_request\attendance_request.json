{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "HR-ARQ-.YY.-.MM.-.#####", "creation": "2018-04-13 15:37:40.918990", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["employee", "employee_name", "department", "company", "column_break_5", "from_date", "to_date", "half_day", "half_day_date", "include_holidays", "shift", "reason_section", "reason", "column_break_4", "explanation", "amended_from"], "fields": [{"fieldname": "employee", "fieldtype": "Link", "in_list_view": 1, "label": "Employee", "options": "Employee", "reqd": 1}, {"fetch_from": "employee.employee_name", "fieldname": "employee_name", "fieldtype": "Data", "label": "Employee Name", "read_only": 1}, {"fetch_from": "employee.department", "fieldname": "department", "fieldtype": "Link", "label": "Department", "options": "Department", "read_only": 1}, {"fetch_from": "employee.company", "fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "remember_last_selected_value": 1, "reqd": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "from_date", "fieldtype": "Date", "in_list_view": 1, "label": "From Date", "reqd": 1}, {"fieldname": "to_date", "fieldtype": "Date", "in_list_view": 1, "label": "To Date", "reqd": 1}, {"default": "0", "fieldname": "half_day", "fieldtype": "Check", "label": "Half Day"}, {"depends_on": "half_day", "fieldname": "half_day_date", "fieldtype": "Date", "label": "Half Day Date", "mandatory_depends_on": "half_day"}, {"fieldname": "reason_section", "fieldtype": "Section Break", "label": "Reason"}, {"fieldname": "reason", "fieldtype": "Select", "in_list_view": 1, "label": "Reason", "options": "Work From Home\nOn Duty", "reqd": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "explanation", "fieldtype": "Small Text", "label": "Explanation"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Attendance Request", "print_hide": 1, "read_only": 1}, {"description": "Note: Shift will not be overwritten in existing attendance records", "fieldname": "shift", "fieldtype": "Link", "label": "Shift", "options": "Shift Type"}, {"default": "0", "fieldname": "include_holidays", "fieldtype": "Check", "label": "Include Holidays"}], "is_submittable": 1, "links": [], "modified": "2024-03-27 13:06:36.343091", "modified_by": "Administrator", "module": "HR", "name": "Attendance Request", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR User", "share": 1, "submit": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Employee", "share": 1, "write": 1}], "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "employee_name", "track_changes": 1}