# HRMS Frontend Auto-Update Setup

This setup provides automatic frontend updates for your HRMS Docker environment. You have several options depending on your needs.

## Quick Start

### For Development (Recommended)
```bash
cd docker
docker-compose -f docker-compose.dev.yml up --build
```

Or on Windows, simply double-click:
```
start-dev.bat
```

### For Production
```bash
cd docker
docker-compose up --build
```

Or on Windows:
```
start-prod.bat
```

## Available Setups

### 1. Development Mode with Hot Reload (`docker-compose.dev.yml`)

**What it does:**
- Runs frontend dev servers with hot reload on ports 8080 and 8081
- Automatically rebuilds and reloads when you save files
- Watches for file changes and rebuilds production assets
- Perfect for active development

**Services:**
- `frontend`: Vue.js dev server with hot reload (port 8080)
- `roster`: Roster dev server with hot reload (port 8081)
- `frontend-builder`: Watches files and builds production assets
- `frappe`: Main Frappe backend (port 8000)
- `mariadb`: Database
- `redis`: Cache

**Access:**
- Main app: http://localhost:8000
- Frontend dev: http://localhost:8080
- Roster dev: http://localhost:8081

### 2. Production Mode (`docker-compose.yml`)

**What it does:**
- Standard production setup
- Serves built assets through Frappe
- No hot reload, but more stable

**Access:**
- Main app: http://localhost:8000

## How Auto-Update Works

### Development Mode
1. **Hot Reload**: The Vite dev servers automatically reload when you change files
2. **File Watcher**: The `frontend-builder` service watches for changes and rebuilds production assets
3. **Cache Clearing**: Automatically clears Frappe cache when assets are rebuilt

### File Watching
The `watch-and-build.sh` script:
- Monitors `frontend/` and `roster/` directories
- Ignores `node_modules`, `.git`, and build directories
- Automatically installs dependencies if `package.json` changes
- Rebuilds assets and clears Frappe cache
- Provides console feedback on build status

## Customization

### Environment Variables
You can customize the setup by setting these environment variables:

```yaml
environment:
  - NODE_ENV=development
  - CHOKIDAR_USEPOLLING=true  # Enable file watching in Docker
  - WATCHPACK_POLLING=true    # Enable webpack polling
```

### Ports
Default ports:
- Frappe: 8000
- Frontend dev: 8080
- Roster dev: 8081

To change ports, edit the `docker-compose.dev.yml` file.

### Volumes
The setup uses named volumes for `node_modules` to improve performance:
- `frontend_node_modules`
- `roster_node_modules`

## Troubleshooting

### File Changes Not Detected
If file changes aren't being detected:
1. Make sure `CHOKIDAR_USEPOLLING=true` is set
2. Try increasing the polling interval in `vite.config.js`
3. Check Docker has access to your files

### Build Failures
If builds fail:
1. Check the logs: `docker-compose -f docker-compose.dev.yml logs frontend-builder`
2. Manually rebuild: `docker-compose -f docker-compose.dev.yml restart frontend-builder`
3. Clear node_modules: `docker-compose -f docker-compose.dev.yml down -v`

### Performance Issues
If the setup is slow:
1. Use named volumes for `node_modules` (already configured)
2. Exclude unnecessary files from watching
3. Reduce polling frequency in Vite config

### Port Conflicts
If ports are already in use:
1. Stop other services using those ports
2. Change ports in `docker-compose.dev.yml`
3. Update your browser bookmarks

## Commands

### Start Development Environment
```bash
docker-compose -f docker-compose.dev.yml up --build
```

### Stop and Clean Up
```bash
docker-compose -f docker-compose.dev.yml down -v
```

### View Logs
```bash
# All services
docker-compose -f docker-compose.dev.yml logs

# Specific service
docker-compose -f docker-compose.dev.yml logs frontend-builder
```

### Rebuild Specific Service
```bash
docker-compose -f docker-compose.dev.yml up --build frontend-builder
```

## Tips

1. **First Run**: The first startup takes longer as it downloads images and installs dependencies
2. **File Permissions**: On Windows, make sure Docker has access to your project directory
3. **Memory**: The development setup uses more memory due to multiple Node.js processes
4. **Hot Reload**: Changes to Vue files should appear immediately in the browser
5. **Production Assets**: Built assets are automatically copied to `hrms/public/frontend/` and `hrms/public/roster/`

## Integration with Your Workflow

This setup integrates with your existing development workflow:
- Edit files in `frontend/` or `roster/` directories
- See changes immediately in development servers
- Production assets are automatically built and available to Frappe
- No need to manually run build commands
- Frappe cache is automatically cleared when needed
