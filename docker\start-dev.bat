@echo off
echo Starting HRMS Development Environment with Auto-Update...
echo.

echo Stopping any existing containers...
docker-compose -f docker-compose.dev.yml down

echo.
echo Starting development environment...
echo This will:
echo - Start MariaDB and Redis
echo - Start Frappe backend on port 8000
echo - Start Frontend dev server on port 8080 (with hot reload)
echo - Start Roster dev server on port 8081 (with hot reload)
echo - Start auto-build watcher for production builds
echo.

docker-compose -f docker-compose.dev.yml up --build

pause
