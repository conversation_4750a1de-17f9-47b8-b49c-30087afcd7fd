#!/bin/bash

echo "🚀 Starting HRMS with Frontend Auto-Build..."

# Function to build frontend
build_frontend() {
    echo "🔄 Building frontend..."
    cd /workspace/hrms/frontend
    
    # Check if Node.js is available, install if needed
    if ! command -v node &> /dev/null; then
        echo "📦 Installing Node.js..."
        curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
        apt-get install -y nodejs
    fi
    
    # Install yarn if not available
    if ! command -v yarn &> /dev/null; then
        echo "📦 Installing Yarn..."
        npm install -g yarn
    fi
    
    # Install dependencies
    echo "📦 Installing frontend dependencies..."
    yarn install --check-files
    
    # Build the frontend
    echo "🏗️  Building frontend assets..."
    yarn build
    
    if [ $? -eq 0 ]; then
        echo "✅ Frontend build completed successfully!"
    else
        echo "❌ Frontend build failed!"
    fi
}

# Function to build roster
build_roster() {
    echo "🔄 Building roster..."
    cd /workspace/hrms/roster
    
    # Install dependencies
    echo "📦 Installing roster dependencies..."
    yarn install --check-files
    
    # Build the roster
    echo "🏗️  Building roster assets..."
    yarn build
    
    if [ $? -eq 0 ]; then
        echo "✅ Roster build completed successfully!"
    else
        echo "❌ Roster build failed!"
    fi
}

# Check if bench already exists
if [ -d "/home/<USER>/frappe-bench/apps/frappe" ]; then
    echo "Bench already exists, skipping init"
    cd frappe-bench
    
    # Build frontend assets
    echo "🎨 Building frontend assets..."
    build_frontend
    build_roster
    
    # Clear cache and start
    echo "🧹 Clearing cache..."
    bench --site hrms.localhost clear-cache
    bench start
else
    echo "Creating new bench..."
    
    export PATH="${NVM_DIR}/versions/node/v${NODE_VERSION_DEVELOP}/bin/:${PATH}"
    
    bench init --skip-redis-config-generation frappe-bench
    
    cd frappe-bench
    
    # Use containers instead of localhost
    bench set-mariadb-host mariadb
    bench set-redis-cache-host redis://redis:6379
    bench set-redis-queue-host redis://redis:6379
    bench set-redis-socketio-host redis://redis:6379
    
    # Remove redis, watch from Procfile
    sed -i '/redis/d' ./Procfile
    sed -i '/watch/d' ./Procfile
    
    bench get-app erpnext
    bench get-app hrms /workspace/hrms
    
    bench new-site hrms.localhost \
        --force \
        --mariadb-root-password 123 \
        --admin-password admin \
        --no-mariadb-socket
    
    bench --site hrms.localhost install-app hrms
    bench --site hrms.localhost set-config developer_mode 1
    bench --site hrms.localhost enable-scheduler
    bench --site hrms.localhost clear-cache
    bench use hrms.localhost
    
    # Build frontend assets after installation
    echo "🎨 Building frontend assets..."
    build_frontend
    build_roster
    
    # Clear cache after building
    bench --site hrms.localhost clear-cache
    
    bench start
fi
