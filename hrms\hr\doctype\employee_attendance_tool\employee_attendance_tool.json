{"actions": [], "allow_copy": 1, "creation": "2016-01-27 14:59:47.849379", "doctype": "DocType", "engine": "InnoDB", "field_order": ["date", "shift", "column_break_gmhs", "late_entry", "early_exit", "section_break_ackd", "company", "branch", "department", "column_break_bhny", "employment_type", "designation", "employee_grade", "get_employees", "select_employees_section", "unmarked_employee_header", "status", "unmarked_employees_html", "horizontal_break", "half_day_marked_employee_header", "half_day_status", "half_marked_employees_html", "marked_attendance_section", "marked_attendance_html"], "fields": [{"default": "Today", "fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "department", "fieldtype": "Link", "label": "Department", "options": "Department"}, {"fieldname": "branch", "fieldtype": "Link", "label": "Branch", "options": "Branch"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company"}, {"collapsible": 1, "depends_on": "date", "fieldname": "marked_attendance_section", "fieldtype": "Section Break", "label": "Marked Attendance"}, {"fieldname": "marked_attendance_html", "fieldtype": "HTML", "label": "Marked Attendance HTML"}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "\nPresent\nAbsent\nHalf Day\nWork From Home"}, {"collapsible": 1, "collapsible_depends_on": "eval:true", "description": "Set filters to fetch employees", "fieldname": "section_break_ackd", "fieldtype": "Section Break", "label": "Get Employees"}, {"fieldname": "column_break_bhny", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "late_entry", "fieldtype": "Check", "label": "Late Entry"}, {"default": "0", "fieldname": "early_exit", "fieldtype": "Check", "label": "Early Exit"}, {"fieldname": "shift", "fieldtype": "Link", "label": "Shift", "options": "Shift Type"}, {"fieldname": "employment_type", "fieldtype": "Link", "label": "Employment Type", "options": "Employment Type"}, {"fieldname": "designation", "fieldtype": "Link", "label": "Designation", "options": "Designation"}, {"fieldname": "employee_grade", "fieldtype": "Link", "label": "Employee Grade", "options": "Employee Grade"}, {"fieldname": "unmarked_employees_html", "fieldtype": "HTML", "label": "Unmarked Employees HTML", "read_only": 1}, {"fieldname": "half_marked_employees_html", "fieldtype": "HTML", "label": "Employees on Half Day HTML"}, {"fieldname": "half_day_status", "fieldtype": "Select", "label": "Status for Other Half", "options": "Present\nAbsent"}, {"fieldname": "column_break_gmhs", "fieldtype": "Column Break"}, {"fieldname": "get_employees", "fieldtype": "<PERSON><PERSON>", "label": "Get Employees"}, {"collapsible": 1, "collapsible_depends_on": "eval:true", "fieldname": "select_employees_section", "fieldtype": "Section Break", "label": "Select Employees"}, {"fieldname": "unmarked_employee_header", "fieldtype": "HTML", "label": "Unmarked Employee Header", "options": "<h5>Unmarked Employees</h5>"}, {"fieldname": "half_day_marked_employee_header", "fieldtype": "HTML", "label": "Half Day Marked Em<PERSON>loyee Header", "options": "<h5>Employees on Half Day</h5>"}, {"fieldname": "horizontal_break", "fieldtype": "HTML", "label": "Horizontal Break", "options": "<hr>"}], "grid_page_length": 50, "hide_toolbar": 1, "issingle": 1, "links": [], "modified": "2025-05-26 09:42:36.888216", "modified_by": "Administrator", "module": "HR", "name": "Employee Attendance Tool", "owner": "Administrator", "permissions": [{"create": 1, "read": 1, "role": "HR Manager", "write": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}