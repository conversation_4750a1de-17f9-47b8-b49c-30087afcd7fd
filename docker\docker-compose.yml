version: "3.8" # Updated version
services:
  mariadb:
    image: mariadb:10.8
    command:
      - --character-set-server=utf8mb4
      - --collation-server=utf8mb4_unicode_ci
      - --skip-character-set-client-handshake
      - --skip-innodb-read-only-compressed # Temporary fix for MariaDB 10.6
    environment:
      MYSQL_ROOT_PASSWORD: 123
    volumes:
      - mariadb-data:/var/lib/mysql

  redis:
    image: redis:alpine

  # Frontend development server with hot reload
  frontend:
    image: node:18-alpine
    working_dir: /app
    volumes:
      - ../frontend:/app
      - /app/node_modules
    ports:
      - 8080:8080
    command: sh -c "yarn install && yarn dev"
    environment:
      - NODE_ENV=development
    depends_on:
      - frappe

  # Roster frontend (if you use it)
  roster:
    image: node:18-alpine
    working_dir: /app
    volumes:
      - ../roster:/app
      - /app/node_modules
    ports:
      - 8081:8081
    command: sh -c "yarn install && yarn dev"
    environment:
      - NODE_ENV=development
    depends_on:
      - frappe

  frappe:
    image: frappe/bench:latest
    command: bash /workspace/init.sh
    environment:
      - SHELL=/bin/bash
    working_dir: /home/<USER>
    volumes:
      - .:/workspace
    ports:
      - 8000:8000
      - 9000:9000

volumes:
  mariadb-data:
